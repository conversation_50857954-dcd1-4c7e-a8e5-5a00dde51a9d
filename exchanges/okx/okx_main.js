const { OkxAPI } = require('./okx_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ ВСЕХ МЕТОДОВ OKX API ===\n');

    const okx = new OkxAPI();

    try {

        // const fundings = await okx.getAllFundings();
        // console.log(`Получено фандинг рейтов: ${fundings.length}`);
        // console.log(fundings);


        // const futuresPrices = await okx.getAllFuturesPrices();
        // console.log(`Получено цен фьючерсов: ${futuresPrices.length}`);
        // console.log(futuresPrices);


        const combinedData = await okx.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);


    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
