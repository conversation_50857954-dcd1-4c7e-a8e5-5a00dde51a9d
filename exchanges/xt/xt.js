const axios = require('axios');

const XT_BASE_URL = 'https://fapi.xt.com';

function formatToken(token) {
    // Преобразование формата токена из базового формата биржи (NTRN-USDT) в формат для нашего файла (ZRO/USDT:USDT)
    const [base, quote] = token.split('-');
    return `${base}/${quote}:USDT`;
}

async function getAllFundingRates() {
    const xtInfo = [];
    const endpoint = '/future/market/v1/public/cg/contracts';

    const options = {
        method: 'GET',
        url: XT_BASE_URL + endpoint,
    }

    try {
        const fundingRates = await axios(options);
        let fundings = fundingRates.data;

        // Обработка данных для соответствия стилю других файлов
        for (const data of fundings) {
            xtInfo.push({
                exchange: 'xt',
                token: formatToken(data.ticker_id), // Применяем форматирование токена
                fundingRate: data.funding_rate * 100, // Конвертируем в проценты
                nextFundingTime: data.next_funding_rate_timestamp
            });
        }

    } catch (error) {
        console.error('Ошибка при запросе:', error);
    }
    // console.log(xtInfo);
    console.log('Данные о фандингах с биржи xt успешно получены');
    return xtInfo; // Возвращаем массив с данными
}

module.exports = {
    getAllFundingRates
};