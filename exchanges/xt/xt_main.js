const { XtAPI } = require('./xt_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ ВСЕХ МЕТОДОВ XT API ===\n');

    const xt = new XtAPI();

    try {

        // const fundings = await xt.getAllFundings();
        // console.log(`Получено фандинг рейтов: ${fundings.length}`);
        // console.log(fundings);
        //
        //
        // const futuresPrices = await xt.getAllFuturesPrices();
        // console.log(`Получено цен фьючерсов: ${futuresPrices.length}`);
        // console.log(futuresPrices);


        const combinedData = await xt.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);


    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
