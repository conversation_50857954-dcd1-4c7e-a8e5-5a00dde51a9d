const { MexcAPI } = require('./mexc_lib');

async function main() {
    console.log('=== ТЕСТИРОВАНИЕ ВСЕХ МЕТОДОВ MEXC API ===\n');

    const mexc = new MexcAPI();

    try {
        // const fundings = await mexc.getAllFundings();
        // console.log(`Получено фандинг рейтов: ${fundings.length}`);
        // console.log(fundings);


        // const futuresPrices = await mexc.getAllFuturesPrices();
        // console.log(`Получено цен фьючерсов: ${futuresPrices.length}`);
        // console.log(futuresPrices);


        const combinedData = await mexc.getCombinedData();
        console.log(`Получено объединенных данных: ${combinedData.length}`);
        console.log(combinedData);


    } catch (error) {
        console.error('Ошибка при выполнении тестов:', error);
    }
}

// Запускаем тестирование
main().catch(console.error);
