
const ccxt = require('ccxt');

async function checkExchangesSupport() {
    console.log('Биржи, поддерживаемые библиотекой ccxt:\n');

    // Получаем список всех поддерживаемых бирж
    const exchanges = ccxt.exchanges;
    
    // Выводим список всех поддерживаемых бирж
    console.log(`Всего поддерживаемых бирж: ${exchanges.length}`);
    console.log(exchanges.join(', '));
    
    // Можно также проверить, поддерживаются ли конкретные биржи
    const specificExchanges = ['binance', 'bybit', 'kucoin', 'okx', 'gate', 'htx', 'bitmex'];
    // console.log('\nПроверка поддержки конкретных бирж:');
    
    // for (const exchange of specificExchanges) {
    //     const isSupported = exchanges.includes(exchange);
    //     console.log(`${exchange}: ${isSupported ? 'поддерживается' : 'не поддерживается'}`);
    // }
}

// Запускаем проверку
checkExchangesSupport().catch(console.error);
